Rails.application.routes.draw do
  apipie

  # Health check
  get "up" => "rails/health#show", as: :rails_health_check

  namespace :api do
    # User info
    get "me", to: "users#me"
    resources :items do
      collection do
        get "by_project/:project_id", action: :by_project
      end
    end

    resources :procurement_requests do
      member do
        post :approve
        post :reject
        post :mark_processing
        post :mark_delivered
        get :approval
      end
    end

    # Analytics & Reporting
    scope :analytics do
      get :dashboard, to: "analytics#dashboard"
      get :spending, to: "analytics#spending"
      get :status, to: "analytics#status"
    end

    scope :reports do
      get :procurement, to: "reports#procurement"
      get :export, to: "reports#export"
    end

    # Categories Management
    resources :categories, only: [ :index ] do
      collection do
        get :stats
        get :popular
      end
      member do
        get :items
      end
    end

    # Approval System (generated by commons gem)
    resources :approval_requests, only: [ :index, :show ] do
      member do
        post :approve
        post :reject
        post :cancel
      end
      collection do
        get :pending_approvals
      end
    end

    # Mount shared session endpoints from auth gem
    mount AtharAuth::Engine, at: "session"
  end

  # Define your application routes per the DSL in https://guides.rubyonrails.org/routing.html

  # Reveal health status on /up that returns 200 if the app boots with no exceptions, otherwise 500.
  # Can be used by load balancers and uptime monitors to verify that the app is live.
  # Note: Health check route already defined above

  # Render dynamic PWA files from app/views/pwa/* (remember to link manifest in application.html.erb)
  # get "manifest" => "rails/pwa#manifest", as: :pwa_manifest
  # get "service-worker" => "rails/pwa#service_worker", as: :pwa_service_worker

  # Defines the root path route ("/")
  root "application#index"
end
