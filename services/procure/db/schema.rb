# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source Rails uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[8.0].define(version: 2025_07_29_093528) do
  # These are extensions that must be enabled in order to support this database
  enable_extension "pg_catalog.plpgsql"

  create_table "approval_actions", force: :cascade do |t|
    t.bigint "approval_request_id", null: false
    t.bigint "approval_step_id", null: false
    t.bigint "user_id", null: false
    t.string "action", null: false
    t.text "comment"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["approval_request_id"], name: "index_approval_actions_on_approval_request_id"
    t.index ["approval_step_id", "user_id", "action"], name: "index_approval_actions_on_step_user_action"
    t.index ["approval_step_id"], name: "index_approval_actions_on_approval_step_id"
  end

  create_table "approval_requests", force: :cascade do |t|
    t.string "workflow_id", null: false
    t.string "workflow_name", null: false
    t.bigint "requestor_id", null: false
    t.bigint "project_id"
    t.string "approvable_type", null: false
    t.bigint "approvable_id", null: false
    t.integer "status", default: 0, null: false
    t.json "steps_data"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["approvable_type", "approvable_id"], name: "index_approval_requests_on_approvable"
    t.index ["project_id"], name: "index_approval_requests_on_project_id"
    t.index ["requestor_id"], name: "index_approval_requests_on_requestor_id"
  end

  create_table "approval_steps", force: :cascade do |t|
    t.bigint "approval_request_id", null: false
    t.string "step_id", null: false
    t.string "name", null: false
    t.integer "sequence", null: false
    t.string "approval_type", default: "any", null: false
    t.json "approver_ids", default: [], null: false
    t.string "condition"
    t.boolean "skip_if_no_approvers", default: false
    t.string "description"
    t.string "dynamic_approver_method"
    t.text "selection_explanation"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["approval_request_id", "sequence"], name: "index_approval_steps_on_approval_request_id_and_sequence", unique: true
    t.index ["approval_request_id"], name: "index_approval_steps_on_approval_request_id"
  end

  create_table "items", force: :cascade do |t|
    t.string "name", null: false
    t.text "description"
    t.string "category", null: false
    t.decimal "approx_price", precision: 10, scale: 2, null: false
    t.bigint "created_by_id", null: false
    t.bigint "project_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["category"], name: "index_items_on_category"
    t.index ["created_by_id"], name: "index_items_on_created_by_id"
    t.index ["project_id"], name: "index_items_on_project_id"
  end

  create_table "procurement_requests", force: :cascade do |t|
    t.bigint "item_id", null: false
    t.bigint "requester_id", null: false
    t.bigint "project_id", null: false
    t.string "status", default: "submitted", null: false
    t.integer "quantity", default: 1, null: false
    t.text "note"
    t.datetime "submitted_at"
    t.datetime "decided_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["item_id"], name: "index_procurement_requests_on_item_id"
    t.index ["project_id"], name: "index_procurement_requests_on_project_id"
    t.index ["requester_id"], name: "index_procurement_requests_on_requester_id"
    t.index ["status"], name: "index_procurement_requests_on_status"
  end

  add_foreign_key "approval_actions", "approval_requests"
  add_foreign_key "approval_actions", "approval_steps"
  add_foreign_key "approval_steps", "approval_requests"
  add_foreign_key "procurement_requests", "items"
end
