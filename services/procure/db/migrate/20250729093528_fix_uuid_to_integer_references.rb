class FixUuidToIntegerReferences < ActiveRecord::Migration[8.0]
  def up
    # Drop existing tables to recreate with proper integer IDs
    drop_table :approval_actions if table_exists?(:approval_actions)
    drop_table :approval_steps if table_exists?(:approval_steps)
    drop_table :approval_requests if table_exists?(:approval_requests)
    drop_table :procurement_requests if table_exists?(:procurement_requests)
    drop_table :items if table_exists?(:items)

    # Recreate items table with integer ID and references
    create_table :items, force: :cascade do |t|
      t.string "name", null: false
      t.text "description"
      t.string "category", null: false
      t.decimal "approx_price", precision: 10, scale: 2, null: false
      t.bigint "created_by_id", null: false
      t.bigint "project_id", null: false
      t.datetime "created_at", null: false
      t.datetime "updated_at", null: false

      t.index [ "category" ], name: "index_items_on_category"
      t.index [ "created_by_id" ], name: "index_items_on_created_by_id"
      t.index [ "project_id" ], name: "index_items_on_project_id"
    end

    # Recreate procurement_requests table with integer ID and references
    create_table :procurement_requests, force: :cascade do |t|
      t.bigint "item_id", null: false
      t.bigint "requester_id", null: false
      t.bigint "project_id", null: false
      t.string "status", default: "submitted", null: false
      t.integer "quantity", default: 1, null: false
      t.text "note"
      t.datetime "submitted_at"
      t.datetime "decided_at"
      t.datetime "created_at", null: false
      t.datetime "updated_at", null: false

      t.index [ "item_id" ], name: "index_procurement_requests_on_item_id"
      t.index [ "project_id" ], name: "index_procurement_requests_on_project_id"
      t.index [ "requester_id" ], name: "index_procurement_requests_on_requester_id"
      t.index [ "status" ], name: "index_procurement_requests_on_status"
    end

    # Add foreign key constraint
    add_foreign_key "procurement_requests", "items"

    # Recreate approval tables with integer references
    create_table "approval_requests", force: :cascade do |t|
      t.string "workflow_id", null: false
      t.string "workflow_name", null: false
      t.bigint "requestor_id", null: false
      t.bigint "project_id"
      t.string "approvable_type", null: false
      t.bigint "approvable_id", null: false
      t.integer "status", default: 0, null: false
      t.json "steps_data"
      t.datetime "created_at", null: false
      t.datetime "updated_at", null: false

      t.index [ "approvable_type", "approvable_id" ], name: "index_approval_requests_on_approvable"
      t.index [ "project_id" ], name: "index_approval_requests_on_project_id"
      t.index [ "requestor_id" ], name: "index_approval_requests_on_requestor_id"
    end

    create_table "approval_steps", force: :cascade do |t|
      t.bigint "approval_request_id", null: false
      t.string "step_id", null: false
      t.string "name", null: false
      t.integer "sequence", null: false
      t.string "approval_type", default: "any", null: false
      t.json "approver_ids", default: [], null: false
      t.string "condition"
      t.boolean "skip_if_no_approvers", default: false
      t.string "description"
      t.string "dynamic_approver_method"
      t.text "selection_explanation"
      t.datetime "created_at", null: false
      t.datetime "updated_at", null: false

      t.index [ "approval_request_id", "sequence" ], name: "index_approval_steps_on_approval_request_id_and_sequence", unique: true
      t.index [ "approval_request_id" ], name: "index_approval_steps_on_approval_request_id"
    end

    create_table "approval_actions", force: :cascade do |t|
      t.bigint "approval_request_id", null: false
      t.bigint "approval_step_id", null: false
      t.bigint "user_id", null: false
      t.string "action", null: false
      t.text "comment"
      t.datetime "created_at", null: false
      t.datetime "updated_at", null: false

      t.index [ "approval_request_id" ], name: "index_approval_actions_on_approval_request_id"
      t.index [ "approval_step_id", "user_id", "action" ], name: "index_approval_actions_on_step_user_action"
      t.index [ "approval_step_id" ], name: "index_approval_actions_on_approval_step_id"
    end

    add_foreign_key "approval_actions", "approval_requests"
    add_foreign_key "approval_actions", "approval_steps"
    add_foreign_key "approval_steps", "approval_requests"
  end

  def down
    # This is destructive - we can't easily revert back to UUIDs
    # without losing data, so we'll just drop the tables
    drop_table :approval_actions if table_exists?(:approval_actions)
    drop_table :approval_steps if table_exists?(:approval_steps)
    drop_table :approval_requests if table_exists?(:approval_requests)
    drop_table :procurement_requests if table_exists?(:procurement_requests)
    drop_table :items if table_exists?(:items)
  end
end
