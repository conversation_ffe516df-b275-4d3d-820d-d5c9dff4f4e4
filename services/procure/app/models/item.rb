class Item < ApplicationRecord
  include Athar::Commons::Models::Concerns::Ransackable

  # Core attributes
  # Note: creator association removed due to AtharAuth User model compatibility
  # Use created_by_id directly and load user data from Core service if needed
  has_many :procurement_requests

  # Validations
  validates :name, presence: true
  validates :category, presence: true
  validates :approx_price, presence: true, numericality: { greater_than_or_equal_to: 0 }
  # validates :created_by_id, presence: true  # Commented out for now due to User model issues
  validates :project_id, presence: true

  # Scopes
  scope :by_category, ->(cat) { where(category: cat) if cat.present? }
  scope :for_project, ->(project_id) { where(project_id: project_id) }
end
