module Api
  class ProcurementRequestsController < ApplicationController
    include AtharAuth::ResourceAuthorization

    before_action :authenticate_session!

    authorize_resources

    private

    def resource_class
      ProcurementRequest
    end

    public

    api! "Lists all procurement requests"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :page, Hash, desc: "Pagination parameters" do
      param :number, Integer, desc: "Page number (default: 1)"
      param :size, Integer, desc: "Items per page (default: 20, max: 100)"
    end
    param :filter, Hash, desc: "Filter parameters" do
      param :status, String, desc: "Filter by status"
      param :project_id, String, desc: "Filter by project (for global users)"
      param :requester_id, String, desc: "Filter by requester"
    end
    param :sort, String, desc: "Sort field (e.g., 'created_at', '-submitted_at' for desc)"
    param :include, String, desc: "Related resources to include"
    param :fields, Hash, desc: "Sparse fieldsets"
    description <<-HTML
      Returns a list of procurement requests.<br>
      Requires permission: <code>:read, :procurement_request</code>.
    HTML
    def index
      @collection = current_user.accessible_requests.includes(:item, :requester)

      apply_filters(@collection) do |filtered_and_sorted|
        records, meta = paginate(filtered_and_sorted)
        serialize_response(records, meta: meta, serializer: ProcurementRequestSerializer)
      end
    end

    api! "Retrieves a specific request"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :id, Integer, required: true, desc: "ID of the request"
    param :include, String, desc: "Related resources to include"
    param :fields, Hash, desc: "Sparse fieldsets"
    description <<-HTML
      Fetches a specific procurement request.<br>
      Requires permission: <code>:read, :procurement_request</code>.
    HTML
    def show
      serialize_response(@procurement_request, serializer: ProcurementRequestSerializer)
    end

    api! "Creates a new procurement request"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :procurement_request, Hash, required: true, desc: "Request details" do
      param :item_id, String, required: true, desc: "ID of the item being requested"
      param :quantity, Integer, required: true, desc: "Quantity requested"
      param :note, String, desc: "Optional employee note"
      param :project_id, String, desc: "Project ID (required for global users, optional for project users)"
    end
    description <<-HTML
      Creates a new procurement request.<br>
      Requires permission: <code>:create, :procurement_request</code>.
    HTML
    def create
      @procurement_request.assign_attributes(procurement_request_params.merge(
        status: "submitted",
        requester_id: current_user.id,
        submitted_at: Time.current
      ))

      if @procurement_request.save
        # Approval workflow automatically triggered via after_create callback
        serialize_response(@procurement_request, serializer: ProcurementRequestSerializer, status: :created)
      else
        serialize_errors(@procurement_request.errors)
      end
    end

    api! "Updates a procurement request"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :id, Integer, required: true, desc: "ID of the request"
    param :procurement_request, Hash, required: true, desc: "Request details to update" do
      param :quantity, Integer, desc: "Quantity requested"
      param :note, String, desc: "Employee note"
    end
    description <<-HTML
      Updates an existing request.<br>
      Requires permission: <code>:update, :procurement_request</code>.
    HTML
    def update
      if @procurement_request.update(procurement_request_params)
        serialize_response(@procurement_request, serializer: ProcurementRequestSerializer)
      else
        serialize_errors(@procurement_request.errors)
      end
    end

    api! "Deletes a procurement request"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :id, Integer, required: true, desc: "ID of the request"
    description <<-HTML
      Deletes a procurement request.<br>
      Requires permission: <code>:destroy, :procurement_request</code>.
    HTML
    def destroy
      @procurement_request.destroy!
      head :no_content
    end

    api! "Approve current step"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :id, Integer, required: true, desc: "ID of the request"
    param :approval_action, Hash, required: true, desc: "Approval action details" do
      param :comment, String, desc: "Optional approval comment"
    end
    def approve
      result = @procurement_request.approval_approve!(
        current_user.id,
        comment: params.dig(:approval_action, :comment)
      )

      if result.success?
        serialize_response({ message: "Request approved successfully" })
      else
        serialize_errors({ detail: result.message })
      end
    end

    api! "Reject current step"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :id, Integer, required: true, desc: "ID of the request"
    param :approval_action, Hash, required: true, desc: "Rejection action details" do
      param :comment, String, required: true, desc: "Rejection comment"
    end
    def reject
      result = @procurement_request.approval_reject!(
        current_user.id,
        comment: params.dig(:approval_action, :comment)
      )

      if result.success?
        serialize_response({ message: "Request rejected successfully" })
      else
        serialize_errors({ detail: result.message })
      end
    end

    api! "Mark as processing"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :id, Integer, required: true, desc: "ID of the request"
    def mark_processing
      if @procurement_request.mark_as_processing!
        serialize_response({ message: "Request marked as processing" })
      else
        serialize_errors({ detail: "Cannot mark as processing" })
      end
    end

    api! "Mark as delivered"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :id, Integer, required: true, desc: "ID of the request"
    def mark_delivered
      if @procurement_request.mark_as_delivered!
        serialize_response({ message: "Request marked as delivered" })
      else
        serialize_errors({ detail: "Cannot mark as delivered" })
      end
    end

    private



    def procurement_request_params
      params.require(:procurement_request).permit(:item_id, :quantity, :note, :project_id)
    end



    def filterable_fields
      %w[status project_id requester_id]
    end

    def sortable_fields
      %w[created_at submitted_at status quantity]
    end
  end
end
