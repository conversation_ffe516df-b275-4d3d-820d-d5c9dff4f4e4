module Api
  class ItemsController < ApplicationController
    include AtharAuth::ResourceAuthorization

    before_action :authenticate_session!

    authorize_resources

    api! "Lists all predefined procurement items"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :sort, String, desc: "Sort field (e.g., 'name', '-created_at' for desc)"
    param :include, String, desc: "Related resources to include"
    param :fields, Hash, desc: "Sparse fieldsets"
    description <<-HTML
      Returns a list of predefined items that can be requested by employees.<br>
      Only Project Managers are allowed to manage these items.<br>
      Requires permission: <code>:read, :item</code>.
    HTML
    returns code: 200, desc: "List of items"

    def index
      @collection = current_user.accessible_items

      apply_filters(@collection) do |filtered_and_sorted|
        records, meta = paginate(filtered_and_sorted)
        serialize_response(records, meta: meta, serializer: ItemSerializer)
      end
    end

    api! "Retrieves a specific item"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :id, Integer, required: true, desc: "ID of the item"
    param :include, String, desc: "Related resources to include"
    param :fields, Hash, desc: "Sparse fieldsets"
    description <<-HTML
      Fetches detailed information about a predefined procurement item.<br>
      Requires permission: <code>:read, :item</code>.
    HTML
    returns code: 200, desc: "Item details"

    def show
      serialize_response(@item, serializer: ItemSerializer)
    end

    api! "Creates a new procurement item"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :item, Hash, required: true, desc: "Item details" do
      param :name, String, required: true, desc: "Item name"
      param :description, String, desc: "Item description"
      param :category, String, required: true, desc: "Item category"
      param :approx_price, Float, required: true, desc: "Approximate price"
      param :project_id, Integer, desc: "Project ID (required for global users)"
    end
    description <<-HTML
      Creates a new predefined procurement item.<br>
      Only Project Managers can create items.<br>
      Requires permission: <code>:create, :item</code>.
    HTML
    returns code: 201, desc: "Item created successfully"
    error code: 422, desc: "Validation errors"

    def create
      @item.assign_attributes(item_params.merge(created_by_id: current_user.id))

      if @item.save
        serialize_response(@item, serializer: ItemSerializer, status: :created)
      else
        serialize_errors(@item.errors)
      end
    end

    api! "Updates a procurement item"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :id, Integer, required: true, desc: "ID of the item"
    param :item, Hash, required: true, desc: "Item details to update" do
      param :name, String, desc: "Item name"
      param :description, String, desc: "Item description"
      param :category, String, desc: "Item category"
      param :approx_price, Float, desc: "Approximate price"
    end
    description <<-HTML
      Updates an existing procurement item.<br>
      Only Project Managers can update items.<br>
      Requires permission: <code>:update, :item</code>.
    HTML
    returns code: 200, desc: "Item updated successfully"
    error code: 422, desc: "Validation errors"

    def update
      if @item.update(item_params)
        serialize_response(@item, serializer: ItemSerializer)
      else
        serialize_errors(@item.errors)
      end
    end

    api! "Deletes a procurement item"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :id, Integer, required: true, desc: "ID of the item"
    description <<-HTML
      Deletes a predefined procurement item.<br>
      Only Project Managers can delete items.<br>
      Requires permission: <code>:destroy, :item</code>.
    HTML
    returns code: 204, desc: "Item deleted successfully"

    def destroy
      @item.destroy!
      head :no_content
    end

    api! "Get items for specific project"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :project_id, String, required: true, desc: "Project ID"
    description <<-HTML
      Gets items for a specific project (global users only).<br>
      Requires permission: <code>:read, :item</code>.
    HTML
    def by_project
      return unless current_user.global_user?

      @collection = Item.for_project(params[:project_id])

      apply_filters(@collection) do |filtered_and_sorted|
        records, meta = paginate(filtered_and_sorted)
        serialize_response(records, meta: meta, serializer: ItemSerializer)
      end
    end

    private

    def item_params
      params.require(:item).permit(:name, :description, :category, :approx_price, :project_id)
    end

    def filterable_fields
      %w[category project_id name]
    end

    def sortable_fields
      %w[name category approx_price created_at updated_at]
    end
  end
end
