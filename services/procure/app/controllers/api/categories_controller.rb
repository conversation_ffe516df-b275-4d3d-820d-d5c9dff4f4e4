module Api
  class CategoriesController < ApplicationController
    before_action :authenticate_session!

    api! "List all item categories"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :page, Hash, desc: "Pagination parameters" do
      param :number, Integer, desc: "Page number (default: 1)"
      param :size, Integer, desc: "Items per page (default: 20, max: 100)"
    end
    param :filter, Hash, desc: "Filter parameters" do
      param :project_id, String, desc: "Filter by specific project (optional for global users)"
      param :name, String, desc: "Filter by category name (partial match)"
    end
    param :sort, String, desc: "Sort field (e.g., 'name', '-items_count' for desc)"
    param :include, String, desc: "Related resources to include"
    param :fields, Hash, desc: "Sparse fieldsets"
    description <<-HTML
      Returns list of all item categories with usage statistics.<br>
      Categories are derived from items in the system.
    HTML
    def index
      return unless authorize!(:read, :item)

      @categories = categories_with_stats

      # Convert to array of objects for pagination
      categories_array = @categories.map { |cat| OpenStruct.new(cat.merge(id: cat[:name])) }

      records, meta = paginate_array(categories_array)
      serialize_response(records, meta: meta, serializer: CategorySerializer)
    end

    api! "Get category usage statistics"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :page, Hash, desc: "Pagination parameters" do
      param :number, Integer, desc: "Page number (default: 1)"
      param :size, Integer, desc: "Items per page (default: 20, max: 100)"
    end
    param :filter, Hash, desc: "Filter parameters" do
      param :project_id, String, desc: "Filter by specific project (optional for global users)"
    end
    param :sort, String, desc: "Sort field (e.g., 'requests_count', '-total_spending' for desc)"
    description <<-HTML
      Returns detailed statistics for each category including item counts, request counts, and spending.
    HTML
    def stats
      return unless authorize!(:read, :analytics)

      @category_stats = detailed_category_stats

      # Convert to array of objects for pagination
      stats_array = @category_stats.map { |stat| OpenStruct.new(stat.merge(id: stat[:name])) }

      records, meta = paginate_array(stats_array)
      serialize_response(records, meta: meta, serializer: CategorySerializer)
    end

    api! "Get most popular categories"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :limit, Integer, desc: "Number of categories to return (default: 10)"
    param :project_id, String, desc: "Filter by specific project (optional for global users)"
    description <<-HTML
      Returns the most popular categories based on request frequency and spending.
    HTML
    def popular
      return unless authorize!(:read, :analytics)

      limit = params[:limit]&.to_i || 10
      @popular_categories = popular_categories_data(limit)

      serialize_response(@popular_categories, serializer: CategorySerializer)
    end

    api! "Get items in a specific category"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :id, String, required: true, desc: "Category name"
    param :page, Hash, desc: "Pagination parameters" do
      param :number, Integer, desc: "Page number (default: 1)"
      param :size, Integer, desc: "Items per page (default: 20, max: 100)"
    end
    param :filter, Hash, desc: "Filter parameters" do
      param :project_id, String, desc: "Filter by specific project (optional for global users)"
    end
    description <<-HTML
      Returns all items in a specific category.
    HTML
    def items
      return unless authorize!(:read, :item)

      category_name = params[:id]
      @collection = current_user.accessible_items.where(category: category_name)

      apply_filters(@collection) do |filtered_and_sorted|
        records, meta = paginate(filtered_and_sorted)
        serialize_response(records, meta: meta, serializer: ItemSerializer)
      end
    end

    private

    def categories_with_stats
      accessible_items = current_user.accessible_items

      if params[:project_id].present? && current_user.global_user?
        accessible_items = accessible_items.where(project_id: params[:project_id])
      end

      categories = accessible_items.group(:category).count

      categories.map do |category, count|
        {
          name: category,
          items_count: count,
          requests_count: category_requests_count(category),
          total_spending: category_total_spending(category),
          average_item_price: category_average_price(category)
        }
      end
    end

    def detailed_category_stats
      accessible_items = current_user.accessible_items

      if params[:project_id].present? && current_user.global_user?
        accessible_items = accessible_items.where(project_id: params[:project_id])
      end

      categories = accessible_items.group(:category).count

      categories.map do |category, count|
        {
          name: category,
          items_count: count,
          requests_count: category_requests_count(category),
          total_spending: category_total_spending(category),
          average_item_price: category_average_price(category),
          most_requested_item: most_requested_item_in_category(category),
          recent_requests_count: recent_category_requests_count(category)
        }
      end
    end

    def popular_categories_data(limit)
      stats = detailed_category_stats

      # Sort by popularity (combination of requests and spending)
      sorted_stats = stats.sort_by do |stat|
        requests = stat[:requests_count] || 0
        spending = stat[:total_spending] || 0.0
        -(requests * 0.7 + spending / 1000 * 0.3) # Negative for descending order
      end

      sorted_stats.first(limit).map { |stat| OpenStruct.new(stat.merge(id: stat[:name])) }
    end

    def category_requests_count(category)
      accessible_requests = current_user.accessible_requests
      accessible_requests.joins(:item).where(items: { category: category }).count
    end

    def category_total_spending(category)
      accessible_requests = current_user.accessible_requests
      accessible_requests.joins(:item)
                        .where(items: { category: category })
                        .sum("items.approx_price * procurement_requests.quantity")
    end

    def category_average_price(category)
      accessible_items = current_user.accessible_items.where(category: category)
      return 0.0 if accessible_items.empty?

      accessible_items.average(:approx_price) || 0.0
    end

    def most_requested_item_in_category(category)
      accessible_requests = current_user.accessible_requests
      item = accessible_requests.joins(:item)
                               .where(items: { category: category })
                               .group("items.id", "items.name")
                               .order("COUNT(*) DESC")
                               .limit(1)
                               .pluck("items.name")
                               .first

      item || "No requests yet"
    end

    def recent_category_requests_count(category)
      accessible_requests = current_user.accessible_requests
      accessible_requests.joins(:item)
                        .where(items: { category: category })
                        .where("procurement_requests.created_at >= ?", 30.days.ago)
                        .count
    end

    def paginate_array(array)
      page_number = params.dig(:page, :number)&.to_i || 1
      page_size = params.dig(:page, :size)&.to_i || 20
      page_size = [ page_size, 100 ].min # Max 100 items per page

      offset = (page_number - 1) * page_size
      paginated_items = array[offset, page_size] || []

      meta = {
        count: array.size,
        page: page_number,
        limit: page_size,
        from: offset + 1,
        to: offset + paginated_items.size
      }

      [ paginated_items, meta ]
    end

    def filterable_fields
      %w[project_id name]
    end

    def sortable_fields
      %w[name items_count requests_count total_spending]
    end
  end
end
