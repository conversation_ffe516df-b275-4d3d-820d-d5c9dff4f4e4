class ItemSerializer
  include JSONAPI::Serializer

  attributes :name, :description, :category, :approx_price, :project_id, :created_at, :updated_at

  attribute :creator do |object|
    {
      id: object.created_by_id,
      name: "User #{object.created_by_id}" # Will be loaded from Core service if needed
    }
  end

  attribute :project do |object|
    # Load project from Core service if needed
    if object.project_id
      {
        id: object.project_id,
        name: "Project #{object.project_id}" # Will be loaded from Core service
      }
    else
      nil
    end
  end

  attribute :procurement_requests_count do |object|
    object.procurement_requests.count
  end

  attribute :total_requested_quantity do |object|
    object.procurement_requests.sum(:quantity)
  end

  attribute :average_request_quantity do |object|
    count = object.procurement_requests.count
    if count.zero?
      0
    else
      object.procurement_requests.sum(:quantity).to_f / count
    end
  end
end
