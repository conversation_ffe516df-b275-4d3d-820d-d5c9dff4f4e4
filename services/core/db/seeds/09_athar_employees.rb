# Employee data from CSV with role mappings using actual email addresses
employees_data = [
  # Regular Employees (using actual emails from users_24_05_2025 14_21_31.csv)
  {
    name: "<PERSON>",
    email: "<EMAIL>", # <PERSON> in email CSV
    role: "procurement_manager"
  },
  {
    name: "<PERSON><PERSON>",
    email: "mayssa<PERSON><PERSON>@atharsociety.org", # <PERSON><PERSON> in email CSV
    role: "graphic_designer"
  },
  {
    name: "<PERSON><PERSON>",
    email: "<EMAIL>", # <PERSON><PERSON> in email CSV
    role: "social_media_specialist"
  },
  {
    name: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
    email: "<EMAIL>", # <PERSON><PERSON><PERSON> in email CSV
    role: "coach"
  },
  {
    name: "<PERSON><PERSON>",
    email: "<EMAIL>", # <PERSON><PERSON> beliah in email CSV
    role: "admin"
  },
  {
    name: "<PERSON><PERSON><PERSON><PERSON>",
    email: "s.al<PERSON><PERSON>@atharsociety.org", # <PERSON><PERSON><PERSON> in email CSV
    role: "coach"
  },
  {
    name: "<PERSON><PERSON>",
    email: "<EMAIL>", # <PERSON>h <PERSON>qeeh in email CSV
    role: "financial_manager" # Override: should be financial_manager
  },
  {
    name: "Alaa Naser Al Zoghbe",
    email: "<EMAIL>", # Alaa Alzoughbi in email CSV
    role: "accountant"
  },
  {
    name: "Samia Hamdi",
    email: "<EMAIL>", # Samia Hamdi in email CSV
    role: "hr_officer"
  },
  {
    name: "Sondos Jamil Mohmmud Abu Khadair",
    email: "<EMAIL>", # Sondos Abukhdeer in email CSV
    role: "project_manager"
  },
  {
    name: "Rahaf Marwan Abdel Jabbar Ismail",
    email: "<EMAIL>", # Rahaf Ismail in email CSV
    role: "case_manager"
  },
  {
    name: "Heba Saleh Mohammad Alzubi",
    email: "<EMAIL>", # Heba Alzoubi in email CSV
    role: "case_manager"
  },
  {
    name: "Abedalhakim Mohammad Soliman Alkawaldeh",
    email: "<EMAIL>", # Hakeem Ghsoon in email CSV
    role: "case_manager"
  },
  {
    name: "Raghad Mahmoud Mohammad Ghanam",
    email: "<EMAIL>", # Raghad Ghanam in email CSV
    role: "psychologist"
  },
  {
    name: "Rama Akram Badiea Khader",
    email: "<EMAIL>", # Rama Abu khader in email CSV
    role: "case_manager"
  },
  {
    name: "Ahmad Saleh Ahmad Alqaisi",
    email: "<EMAIL>", # Ahmad Alqaisi in email CSV
    role: "coach"
  },
  {
    name: "Shoroq Naim Jaber Alsaqkri",
    email: "<EMAIL>", # Shuruq Asakree in email CSV
    role: "coach"
  },
  {
    name: "Dania Khaled Bakeer",
    email: "<EMAIL>", # Dania Bakeer in email CSV
    role: "case_manager"
  },
  {
    name: "Sara Zakaria Lutfi Jaber",
    email: "<EMAIL>", # Sara Jaber in email CSV
    role: "case_manager"
  },
  {
    name: "Tasnim Rafat Abdel Rahim Saleh",
    email: "<EMAIL>", # Tasneem Saleh in email CSV
    role: "case_manager"
  },
  {
    name: "Esraa Mohammad Tawfik Alkhaderee",
    email: "<EMAIL>", # Esra'a Al-Khudari in email CSV
    role: "case_manager"
  },
  {
    name: "Reem Ramzi Hasan Alowaisy",
    email: "<EMAIL>", # Reem Alowaisy in email CSV
    role: "club_facilitator"
  },
  {
    name: "Saeed Jamal Al Eleisah",
    email: "<EMAIL>", # Saeed Sanory in email CSV
    role: "project_manager"
  },
  {
    name: "Ola Mohammad Estaitia",
    email: "<EMAIL>", # Ola Estaita in email CSV
    role: "program_manager"
  },
  {
    name: "Abdalrhman Mousa",
    email: "<EMAIL>", # Abdalrhman Mousa in email CSV (MEAL Officer - skipped duplicate)
    role: "hr_manager"
  },

  # Missing employees from original CSV (generating emails)
  {
    name: "Jenan Jamel Khaleel Yousef",
    email: "<EMAIL>", # Generated email
    role: "graphic_designer"
  },
  {
    name: "Samar Mahmoud Abdallah Alkhatib",
    email: "<EMAIL>", # Generated email
    role: "housekeeping"
  },
  {
    name: "Rawan Ibraheem Abdalah Hseen",
    email: "<EMAIL>", # Generated email
    role: "coach"
  },
  {
    name: "Eman Ayman Ahmad Ibrahim",
    email: "<EMAIL>", # Generated email
    role: "coach"
  },
  {
    name: "Oday Mohammad Ali Estaitia",
    email: "<EMAIL>", # Generated email
    role: "coach"
  },
  {
    name: "Mohammad Amer Asi",
    email: "<EMAIL>", # Generated email
    role: "community_mobilizer"
  },
  {
    name: "Yaqin Wael Ayoub Mousa",
    email: "<EMAIL>", # Generated email
    role: "community_mobilizer"
  },
  {
    name: "Ata Mohammad Elhaj",
    email: "<EMAIL>", # Generated email
    role: "club_facilitator"
  },
  {
    name: "Dila",
    email: "<EMAIL>", # Existing email from system
    role: "employee" # Generic role - needs to be updated with actual role
  },
  # Skip duplicate: Abedalrahman wael ayoub musa (MEAL OFFICER)

  # Contract Workers/Volunteers (using existing and generated emails)
  {
    name: "Sondos Farhan A. Alhayek",
    email: "<EMAIL>", # Sondos Hayek in email CSV
    role: "volunteer"
  },
  {
    name: "Walaa Qondos", # Adding from email CSV
    email: "<EMAIL>", # Walaa Qondos in email CSV
    role: "volunteer"
  },

  # Additional volunteers with generated emails
  {
    name: "Bayan Mohammad Said Abushalha",
    email: "<EMAIL>", # Generated email
    role: "volunteer"
  },
  {
    name: "Maram Othman Mustafa Mohammad",
    email: "<EMAIL>", # Generated email
    role: "volunteer"
  },
  {
    name: "Aws Abd Elhakim Mohd Abu Jado",
    email: "<EMAIL>", # Generated email
    role: "volunteer"
  },
  {
    name: "Abedalrahman Ayoub Hasan Ismail",
    email: "<EMAIL>", # Generated email
    role: "volunteer"
  },
  {
    name: "Raniam Rami Alfaqeeh",
    email: "<EMAIL>", # Generated email
    role: "volunteer"
  },

  # Community Animators with generated emails
  {
    name: "Fatima Rajeh Khaleel",
    email: "<EMAIL>", # Generated email
    role: "community_animator"
  },
  {
    name: "Ahmad Mahmoud Ahmad Dalal",
    email: "<EMAIL>", # Generated email
    role: "community_animator"
  },
  {
    name: "Maysoon Yousef Suleiman Hejawi",
    email: "<EMAIL>", # Generated email
    role: "community_animator"
  },
  {
    name: "Salsabil Faisal Yousef Abdalgani",
    email: "<EMAIL>", # Generated email
    role: "community_animator"
  },
  {
    name: "Anwar Fatehi Shehadeh Abuajjuir",
    email: "<EMAIL>", # Generated email
    role: "community_animator"
  },
  {
    name: "Wejdan Rafiq Orabi Hamdan",
    email: "<EMAIL>", # Generated email
    role: "community_animator"
  },
  {
    name: "Wessam Taiel Farhan Albashhbsheh",
    email: "<EMAIL>", # Generated email
    role: "community_animator"
  },
  {
    name: "Mahmoud Yaser Abdalslam Abdil Jaleel",
    email: "<EMAIL>", # Generated email
    role: "community_animator"
  },
  {
    name: "Ehab Jihad Ahmad Al-Bazz",
    email: "<EMAIL>", # Generated email
    role: "community_animator"
  },
  {
    name: "Bayan Hayel Farhan Madah",
    email: "<EMAIL>", # Generated email
    role: "community_animator"
  },
  {
    name: "Wafaa Suleiman Hasan Alhkook",
    email: "<EMAIL>", # Generated email
    role: "community_animator"
  }
]

# Create employees and assign roles
employees_data.each do |employee_data|
  user = User.find_or_create_by!(email: employee_data[:email]) do |u|
    u.name = employee_data[:name]
    u.password = "password"
    u.status = 'active'
    u.global = false # Most employees are project-based
  end

  # Determine if role should be global or project-based
  role = Role.find_by!(name: employee_data[:role]) # Fail if role doesn't exist

  if role.global_role?
    # Global roles (hr_officer, hr_manager, financial_manager, accountant)
    # IMPORTANT: Set user as global BEFORE adding global roles
    user.update!(global: true) unless user.global?

    # For global users, add role to the organization project (not nil)
    org_project = Project.find_by!(name: AtharAuth.organization_project_name)
    success = user.add_role(employee_data[:role], org_project)

    # Set as default project for this user if role was added successfully
    if success && !user.user_roles.exists?(is_default: true)
      user_role = user.user_roles.find_by(role: role, project: org_project)
      user_role&.update!(is_default: true)
    end
  else
    # Project-based roles - assign to default project
    default_project = Project.first! # Fail if no project exists
    user.add_role(employee_data[:role], default_project)

    # Set as default project for this user
    unless user.user_roles.exists?(is_default: true)
      user_role = user.user_roles.find_by!(project: default_project)
      user_role.update!(is_default: true)
    end
  end
end

puts "✅ Employees seeded successfully!"
puts "📊 Created #{employees_data.length} employees from CSV data"
