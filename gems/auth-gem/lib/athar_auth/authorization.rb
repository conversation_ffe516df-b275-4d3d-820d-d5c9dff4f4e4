# frozen_string_literal: true

require "active_support/concern"
require_relative "controller_helpers"
require_relative "token_compression"

module AtharAuth
  module Authorization
    extend ActiveSupport::Concern

    included do
      if respond_to?(:helper_method)
        helper_method :current_permissions, :can?, :authorize!, :current_role, :current_project,
                      :global_user?, :accessible_project_ids, :user_can_access_project?,
                      :organization_project?, :global_user_in_organization?,
                      :authorize_resources
      end
    end

    # Returns the permissions array from the expanded token
    def current_permissions
      @current_permissions ||= decoded_token&.dig('session', 'permissions') || []
    end

    # Enhanced can? with automatic project validation
    def can?(action, subject, options = {})
      if subject.is_a?(Symbol) || subject.is_a?(String)
        # Symbol: explicit project context from caller
        can_general_action?(action, subject, options)
      else
        # Instance: automatic project validation via project_id
        can_instance_action?(action, subject, options)
      end
    end

    private

    def can_general_action?(action, subject, options = {})
      base = subject.to_s.underscore
      project = options[:project]

      # Find the best matching permission for this user
      matched_permission = find_best_permission(action, base)
      return false unless matched_permission

      # Global users in organization project can access anything they have permissions for
      return true if global_user_in_organization?

      # Regular global users (shouldn't happen with new architecture)
      return true if global_user?

      # If project context provided, validate it
      return user_can_access_project?(project) if project.present?

      # No project context = caller says it's not needed (AtharPeople)
      true
    end

    def can_instance_action?(action, instance, options = {})
      # Check general permission first
      subject_type = instance.class.name.underscore.to_sym
      return false unless can_general_action?(action, subject_type, options)

      # Automatic project validation using convention
      if instance.respond_to?(:project_id) && instance.project_id.present?
        return true if global_user?

        return user_can_access_project?(instance.project_id)
      end

      # No project_id = not project-based (AtharPeople), permission check was enough
      true
    end

    public

    # Enhanced authorize! method with project context
    # @param action [Symbol, String] The action to authorize
    # @param subject [Symbol, String, Object] The subject to authorize
    # @param options [Hash] Additional options
    # @return [Boolean] True if authorized, renders error and returns false otherwise
    def authorize!(action, subject, options = {})
      unless user_signed_in?
        render_unauthorized("You must be signed in")
        return false
      end

      return true if can?(action, subject, options)

      subject_name = extract_subject_name(subject)
      render_forbidden("You are not allowed to #{action} this #{subject_name}")
      false
    end

    # Enhanced ActiveStruct instance methods - complete objects with all information
    # Uses expanded token data from single entry point
    def current_project
      @current_project ||= begin
        project_data = decoded_token&.dig('session', 'project')
        AtharAuth::Models::Project.from_token_data(project_data)
      end
    end

    def current_role
      @current_role ||= begin
        role_data = decoded_token&.dig('session', 'role')
        AtharAuth::Models::Role.from_token_data(role_data)
      end
    end

    def current_user
      @current_user ||= AtharAuth::Models::User.from_token_data(decoded_token)
    end

    # Convenience methods for common checks (avoid fragmentation)
    def global_user?
      current_user&.global_user? || false
    end

    def organization_project?
      current_project&.name == AtharAuth.organization_project_name
    end

    def global_user_in_organization?
      global_user? && organization_project?
    end

    # Session+project specific access methods (using expanded token data)
    def accessible_project_ids
      @accessible_project_ids ||= if global_user?
                                    # Global user can access all projects in this system
                                    all_projects = decoded_token&.dig('access', 'projects')&.keys&.map(&:to_i) || []

                                    # Always include organization project for global users
                                    org_project_id = Project.find_by(name: AtharAuth.organization_project_name)&.id
                                    all_projects << org_project_id if org_project_id

                                    all_projects.uniq
                                  else
                                    # Project user: current project + other accessible projects
                                    current_id = current_project&.id
                                    other_projects = decoded_token&.dig('access', 'other_projects') || []
                                    other_ids = other_projects.map { |p| p['id'] }

                                    ([current_id] + other_ids).compact.uniq
                                  end
    end

    def accessible_projects_with_names
      @accessible_projects_with_names ||= if global_user?
                                            decoded_token&.dig('access', 'projects') || {}
                                          else
                                            result = {}

                                            # Add current project
                                            result[current_project.id] = current_project.name if current_project

                                            # Add other projects
                                            other_projects = decoded_token&.dig('access', 'other_projects') || []
                                            other_projects.each do |project|
                                              result[project['id']] = project['name'] # 'name' from expanded format
                                            end

                                            result
                                          end
    end

    def can_access_other_projects?
      if global_user?
        true
      else
        other_projects = decoded_token&.dig('access', 'other_projects') || []
        other_projects.any?
      end
    end

    # Project access validation (compressed token support)
    def user_can_access_project?(project)
      return true if global_user?
      return false unless project

      project_id = project.respond_to?(:id) ? project.id : project.to_i
      accessible_project_ids.include?(project_id)
    end

    # Method call style resource authorization with automatic collection preparation
    # @param action [Symbol, String] The action to authorize (e.g., :index, :show, :create)
    # @param resource_class [Class, String, Symbol] The resource class to authorize
    # @param options [Hash] Additional options
    # @option options [Symbol] :collection_name Override the instance variable name for collections
    # @option options [Symbol] :resource_name Override the instance variable name for single resources
    # @option options [Hash] :scope Additional scoping conditions
    # @return [Object, Array] The authorized resource(s) or raises authorization error
    def authorize_resources(action, resource_class, options = {})
      unless user_signed_in?
        render_unauthorized("You must be signed in")
        return false
      end

      # Normalize resource class (let programming errors bubble up)
      klass = normalize_resource_class(resource_class)

      # Determine if this is a collection or single resource action
      collection_action = collection_action?(action)

      if collection_action
        authorize_collection(action, klass, options)
      else
        authorize_single_resource(action, klass, options)
      end
    end

    # Legacy method for backward compatibility
    def current_roles
      @current_roles ||= current_role ? [current_role.name] : []
    end

    private

    # Map REST actions to permission actions
    def map_action_to_permission(action)
      action_mapping = {
        index: :read,
        show: :read,
        create: :create,
        update: :update,
        destroy: :destroy,
        edit: :read,
        new: :create
      }

      action_mapping[action.to_sym] || action.to_sym
    end

    # Check for hierarchical permission patterns
    # Returns the best matching permission for the user
    def find_best_permission(action, subject_name)
      permission_action = map_action_to_permission(action)
      Rails.logger.debug "[AtharAuth] find_best_permission: action=#{action} -> permission_action=#{permission_action}"

      # Try permissions in order of preference:
      # 1. manage:subject (full access)
      # 2. action:subject (direct permission)
      # 3. action_project:subject (project-scoped access)
      # 4. action_own:subject (own resources only - AtharPeople pattern)
      # 5. action_self:subject (self resources only - legacy pattern)

      permission_candidates = [
        "manage:#{subject_name}",
        "#{permission_action}:#{subject_name}",
        "#{permission_action}_project:#{subject_name}",
        "#{permission_action}_own:#{subject_name}",
        "#{permission_action}_self:#{subject_name}"
      ]

      Rails.logger.debug "[AtharAuth] permission_candidates: #{permission_candidates}"
      Rails.logger.debug "[AtharAuth] current_permissions: #{current_permissions}"

      # Return the first permission the user has
      permission_candidates.each do |permission|
        if current_permissions.include?(permission)
          Rails.logger.debug "[AtharAuth] FOUND permission: #{permission}"
          return permission
        end
      end

      Rails.logger.debug "[AtharAuth] NO PERMISSION FOUND"
      nil
    end

    # Extract permission type from matched permission
    def extract_permission_type(permission, subject_name)
      # Handle nil permission case
      return :no_access if permission.nil?

      if permission == "manage:#{subject_name}"
        :manage
      elsif permission.end_with?("_project:#{subject_name}")
        :project_scoped
      elsif permission.end_with?("_own:#{subject_name}")
        :own_only
      elsif permission.end_with?("_self:#{subject_name}")
        :own_only
      else
        :full_access
      end
    end

    # Normalize resource class from various input types
    def normalize_resource_class(resource_class)
      case resource_class
      when Class
        resource_class
      when String
        resource_class.constantize
      when Symbol
        resource_class.to_s.classify.constantize
      else
        raise ArgumentError, "Invalid resource class: #{resource_class}"
      end
    end

    # Determine if action is a collection action
    # Collection actions operate on multiple resources and don't require a specific resource ID
    def collection_action?(action)
      action_sym = action.to_sym

      Rails.logger.debug "[AtharAuth] collection_action? called with action: #{action_sym}"

      # Check if this is a standard Rails collection action
      # Rails defines collection actions as those that don't operate on a specific member
      if action_sym == :index
        Rails.logger.debug "[AtharAuth] #{action_sym} is standard index action -> COLLECTION"
        return true
      end

      # Check if the current route (if available) is a collection route
      if respond_to?(:request) && request.present?
        Rails.logger.debug "[AtharAuth] Request available, checking route..."
        begin
          # Get the current route from Rails router
          route = Rails.application.routes.router.recognize(request) rescue nil
          Rails.logger.debug "[AtharAuth] Route recognized: #{route}"
          if route && route[:action] == action.to_s
            # Check if the route pattern includes :id parameter
            # Collection routes typically don't have :id in their path
            route_path = Rails.application.routes.router.route_for(route)&.path&.spec&.to_s
            Rails.logger.debug "[AtharAuth] Route path: #{route_path}"
            has_id = route_path&.include?(':id')
            Rails.logger.debug "[AtharAuth] Route has :id parameter: #{has_id}"
            if route_path
              result = !has_id
              Rails.logger.debug "[AtharAuth] Route-based decision: #{result ? 'COLLECTION' : 'MEMBER'}"
              return result
            end
          end
        rescue StandardError => e
          Rails.logger.debug "[AtharAuth] Route inspection failed: #{e.message}"
          # Fall back to heuristic if route inspection fails
        end
      else
        Rails.logger.debug "[AtharAuth] No request available for route inspection"
      end

      # Heuristic: Check if params[:id] is expected for this action
      # Collection actions typically don't require an ID parameter
      if respond_to?(:params) && params.present?
        Rails.logger.debug "[AtharAuth] Params available: #{params.keys}"
        id_present = params[:id].present?
        id_required = action_requires_id?(action_sym)
        Rails.logger.debug "[AtharAuth] ID present: #{id_present}, ID required: #{id_required}"
        # If no ID is provided and none is expected, likely a collection action
        if params[:id].blank? && !id_required
          Rails.logger.debug "[AtharAuth] No ID and none required -> COLLECTION"
          return true
        end
      else
        Rails.logger.debug "[AtharAuth] No params available for heuristic"
      end

      # Default to single resource action for safety
      Rails.logger.debug "[AtharAuth] Defaulting to MEMBER action"
      false
    end

    private

    # Determine if an action semantically requires an ID parameter
    def action_requires_id?(action)
      # Standard member actions that require ID
      member_actions = %i[show edit update destroy]
      return true if member_actions.include?(action)

      # Actions that typically don't require ID (collection-like)
      collection_like_actions = %i[new index search filter export import]
      return false if collection_like_actions.include?(action)

      # For custom actions, assume they require ID unless proven otherwise
      # This is the safer default to prevent unauthorized access
      true
    end

    # Authorize collection actions with automatic filtering
    def authorize_collection(action, klass, options)
      # Check permission and get the matched permission
      subject_name = klass.name.underscore
      unless can?(action, subject_name)
        render_forbidden("You are not allowed to #{action} #{subject_name.pluralize}")
        return false
      end

      # Build base scope
      scope = build_base_scope(klass, options)

      # Determine permission type and apply filtering
      matched_permission = find_best_permission(action, subject_name)
      permission_type = extract_permission_type(matched_permission, subject_name)
      puts "🔥 [AUTH DEBUG] matched_permission: #{matched_permission}, permission_type: #{permission_type}"
      scope = apply_permission_based_filtering(scope, klass, permission_type)

      # Apply additional scoping from options
      scope = apply_additional_scoping(scope, options[:scope]) if options[:scope]

      # Set instance variable
      collection_name = options[:collection_name] || "@#{klass.name.underscore.pluralize}"
      instance_variable_set(collection_name, scope)

      scope
    end

    # Authorize single resource actions
    def authorize_single_resource(action, klass, options)
      # Get resource (usually from params[:id])
      resource = find_resource(klass, options)

      # If find_resource returned false, it already rendered an error response
      return false if resource == false

      # Check basic permission
      unless can?(action, resource)
        render_forbidden("You are not allowed to #{action} this #{klass.name.underscore}")
        return false
      end

      # Set instance variable
      resource_name = options[:resource_name] || "@#{klass.name.underscore}"
      instance_variable_set(resource_name, resource)

      resource
    end

    # Build base scope for the resource
    def build_base_scope(klass, _options)
      if klass.respond_to?(:accessible_to)
        # Use custom accessible_to scope if available
        klass.accessible_to(current_user)
      else
        # Default to all records
        klass.all
      end
    end

    # Check if resource supports project-based filtering
    def supports_project_filtering?(klass)
      klass.column_names.include?('project_id') && !global_user?
    end

    # Apply filtering based on permission type
    def apply_permission_based_filtering(scope, klass, permission_type)
      if defined?(Rails)
        Rails.logger.debug("[AtharAuth] apply_permission_based_filtering: permission_type=#{permission_type}, klass=#{klass}")
      end

      case permission_type
      when :no_access
        # No permission found - return empty scope
        Rails.logger.debug("[AtharAuth] No permission found, returning empty scope") if defined?(Rails)
        scope.none
      when :manage, :full_access
        # Full access - apply standard project filtering if applicable
        Rails.logger.debug("[AtharAuth] Applying standard project filtering") if defined?(Rails)
        apply_standard_project_filtering(scope, klass)
      when :project_scoped
        # Project-scoped access - use model's project filtering if available
        Rails.logger.debug("[AtharAuth] Applying project scoped filtering") if defined?(Rails)
        apply_project_scoped_filtering(scope, klass)
      when :own_only
        # Own resources only - use model's own resource filtering if available
        Rails.logger.debug("[AtharAuth] Applying own resource filtering") if defined?(Rails)
        apply_own_resource_filtering(scope, klass)
      else
        # Default to standard project filtering
        Rails.logger.debug("[AtharAuth] Applying default standard project filtering") if defined?(Rails)
        apply_standard_project_filtering(scope, klass)
      end
    end

    # Apply standard project-based filtering (original logic)
    def apply_standard_project_filtering(scope, klass)
      if supports_project_filtering?(klass)
        scope.where(project_id: accessible_project_ids)
      else
        scope
      end
    end

    # Apply project-scoped filtering using Rails scope conventions
    def apply_project_scoped_filtering(scope, klass)
      # 1. Check if model defines custom project scope
      if klass.respond_to?(:for_projects)
        scope.for_projects(accessible_project_ids)
      # 2. Standard project_id filtering
      elsif supports_project_filtering?(klass)
        scope.where(project_id: accessible_project_ids)
      # 3. No project filtering available
      else
        scope
      end
    end

    # Apply own resource filtering using Rails scope conventions
    def apply_own_resource_filtering(scope, klass)
      puts "🔥 [AUTH DEBUG] apply_own_resource_filtering: klass=#{klass}, current_user=#{current_user&.id}"
      if defined?(Rails)
        Rails.logger.debug("[AtharAuth] apply_own_resource_filtering: klass=#{klass}, current_user=#{current_user&.id}")
      end

      # 1. Check if model defines custom own resource scope
      if klass.respond_to?(:for_user)
        puts "🔥 [AUTH DEBUG] Using #{klass}.for_user(current_user)"
        Rails.logger.debug("[AtharAuth] Using #{klass}.for_user(current_user)") if defined?(Rails)
        result = scope.for_user(current_user)
        puts "🔥 [AUTH DEBUG] for_user scope returned: #{result.to_sql}"
        Rails.logger.debug("[AtharAuth] for_user scope returned: #{result.to_sql}") if defined?(Rails)
        result
      # 2. Standard user_id filtering
      elsif scope.respond_to?(:where) && scope.column_names.include?('user_id')
        Rails.logger.debug("[AtharAuth] Using standard user_id filtering") if defined?(Rails)
        scope.where(user_id: current_user.id)
      # 3. No own resource filtering available
      else
        Rails.logger.debug("[AtharAuth] No own resource filtering available, returning scope.none") if defined?(Rails)
        scope.none
      end
    end

    # Apply additional scoping conditions
    def apply_additional_scoping(scope, additional_scope)
      case additional_scope
      when Hash
        scope.where(additional_scope)
      when Proc
        additional_scope.call(scope)
      else
        scope
      end
    end

    # Find single resource for authorization
    def find_resource(klass, options)
      if options[:resource]
        options[:resource]
      elsif params[:id]
        # Apply project filtering if supported
        if supports_project_filtering?(klass)
          begin
            klass.where(project_id: accessible_project_ids).find(params[:id])
          rescue ActiveRecord::RecordNotFound
            # If record not found due to project filtering, render forbidden instead
            render_forbidden("You are not allowed to access this #{klass.name.underscore}")
            false
          end
        else
          klass.find(params[:id])
        end
      elsif supports_project_filtering?(klass)
        # For create actions, build new resource
        klass.new(project_id: current_project&.id)
      else
        klass.new
      end
    end

    # Extract subject name from various input types
    # @param subject [Symbol, String, Object] The subject to extract name from
    # @return [String] The extracted subject name
    def extract_subject_name(subject)
      if subject.is_a?(Symbol) || subject.is_a?(String)
        subject.to_s
      else
        subject.class.name.underscore
      end
    end
  end
end
