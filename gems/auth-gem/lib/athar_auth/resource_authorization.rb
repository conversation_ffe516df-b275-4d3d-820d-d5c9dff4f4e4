# frozen_string_literal: true

module AtharAuth
  module ResourceAuthorization
    extend ActiveSupport::Concern

    class_methods do
      # Method call style (like acts_as_approvable)
      def authorize_resources(options = {})
        # Store configuration
        class_attribute :authorization_config, default: options
        class_attribute :action_permission_mappings, default: {}

        # Set up before_actions based on configuration
        setup_authorization_callbacks(options)
      end

      # Map action to custom permission or authorization logic
      # @param action [Symbol] The action name
      # @param permission_or_proc [Symbol, Proc] Either a permission symbol or custom authorization proc
      # @example Simple permission mapping
      #   map_action_permission :render_data, :read
      # @example Custom authorization logic
      #   map_action_permission :render_data, -> { can?(:read, :form_template) && custom_condition? }
      def map_action_permission(action, permission_or_proc)
        self.action_permission_mappings ||= {}
        self.action_permission_mappings[action.to_sym] = permission_or_proc

        # Only auto-include action if using explicit 'only:' mode
        current_config = authorization_config || {}

        if current_config[:only]
          # EXISTING BEHAVIOR: explicit only list - add action if not included
          only_actions = current_config[:only]
          unless only_actions.include?(action.to_sym)
            updated_config = current_config.merge(only: only_actions + [action.to_sym])
            self.authorization_config = updated_config
            setup_authorization_callbacks(updated_config)
          end
        else
          # NEW BEHAVIOR: secure by default or except mode - don't interfere
          # The action will be handled by setup_default_authorization
        end
      end

      # Detect if an action is semantically a collection action
      # Collection actions operate on multiple resources and don't require a specific resource ID
      def detect_collection_action_semantically(action)
        action_sym = action.to_sym

        # Standard REST collection actions
        return true if action_sym == :index

        # Actions that typically don't require ID (collection-like)
        collection_like_actions = %i[new search filter export import sequence]
        return true if collection_like_actions.include?(action_sym)

        # Check naming conventions that indicate collection actions
        action_str = action_sym.to_s
        collection_suffixes = %w[_list _all _search _export _import _bulk _summary _stats _analytics]
        collection_prefixes = %w[list_ search_ filter_ export_ import_ bulk_]

        return true if collection_suffixes.any? { |suffix| action_str.end_with?(suffix) }
        return true if collection_prefixes.any? { |prefix| action_str.start_with?(prefix) }

        # Default to single resource action for safety
        false
      end

      private

      def setup_authorization_callbacks(options)
        Rails.logger.debug "[AtharAuth] Controller: #{self.name}"
        Rails.logger.debug "[AtharAuth] Options: #{options}"

        if options[:only]
          # EXISTING BEHAVIOR: explicit only list (backward compatible)
          setup_explicit_authorization(options)
        else
          # NEW BEHAVIOR: all actions (secure by default)
          setup_default_authorization(options)
        end
      end

      def setup_explicit_authorization(options)
        # Default actions that get auto-authorization
        auto_actions = options[:only]
        # Intelligently determine collection actions if not explicitly specified
        if options[:collection_actions]
          collection_actions = options[:collection_actions]
        else
          # Use semantic detection for collection actions
          collection_actions = auto_actions.select { |action| detect_collection_action_semantically(action) }
        end
        skip_actions = options[:except] || []

        Rails.logger.debug "[AtharAuth] auto_actions: #{auto_actions}"
        Rails.logger.debug "[AtharAuth] collection_actions: #{collection_actions}"
        Rails.logger.debug "[AtharAuth] skip_actions: #{skip_actions}"

        # Remove skipped actions
        auto_actions -= skip_actions
        collection_actions -= skip_actions

        Rails.logger.debug "[AtharAuth] Final auto_actions: #{auto_actions}"
        Rails.logger.debug "[AtharAuth] Final collection_actions: #{collection_actions}"
        Rails.logger.debug "[AtharAuth] Single resource actions: #{auto_actions - collection_actions}"

        # Set up callbacks with explicit action lists
        before_action :authorize_and_load_collection!, only: collection_actions
        before_action :authorize_and_load_resource!, only: (auto_actions - collection_actions)
      end

      def setup_default_authorization(options)
        # NEW BEHAVIOR: authorize all actions by default
        skip_actions = options[:except] || []

        Rails.logger.debug "[AtharAuth] Authorizing ALL actions"
        Rails.logger.debug "[AtharAuth] skip_actions: #{skip_actions}"

        # Set up dynamic callback for all actions
        if skip_actions.any?
          before_action :authorize_and_load_dynamic!, except: skip_actions
        else
          before_action :authorize_and_load_dynamic!
        end
      end
    end

    private

    def authorize_and_load_dynamic!
      Rails.logger.debug "[AtharAuth] authorize_and_load_dynamic! called for #{controller_name}##{action_name}"

      if self.class.detect_collection_action_semantically(action_name.to_sym)
        authorize_and_load_collection!
      else
        authorize_and_load_resource!
      end
    end

    def authorize_and_load_collection!
      Rails.logger.debug "[AtharAuth] authorize_and_load_collection! called for #{controller_name}##{action_name}"

      action_sym = action_name.to_sym
      resource_class = controller_name.classify.constantize

      # Check for custom permission mapping first
      if self.class.action_permission_mappings&.key?(action_sym)
        permission_or_proc = self.class.action_permission_mappings[action_sym]

        # Handle custom authorization
        authorized = if permission_or_proc.is_a?(Proc)
                       # Execute custom authorization proc in controller context
                       instance_eval(&permission_or_proc)
                     else
                       # Simple permission mapping
                       can?(permission_or_proc, resource_class.name.underscore.to_sym)
                     end

        unless authorized
          render_forbidden("You are not allowed to #{action_name} #{resource_class.name.underscore.pluralize}")
          return false
        end

        # If authorized, load the collection using the mapped permission
        mapped_action = permission_or_proc.is_a?(Proc) ? action_sym : permission_or_proc
        collection = authorize_collection(mapped_action, resource_class, {})

        # Set instance variable
        instance_variable_set("@#{controller_name}", collection)
        collection
      else
        # Use default authorization logic
        options = {}
        collection = authorize_collection(action_sym, resource_class, options)

        # Set instance variable with resource name (like @cases, @employees)
        instance_variable_set("@#{controller_name}", collection)
        collection
      end
    end

    def authorize_and_load_resource!
      Rails.logger.debug "[AtharAuth] authorize_and_load_resource! called for #{controller_name}##{action_name}"

      action_sym = action_name.to_sym
      resource_class = controller_name.classify.constantize

      # Check for custom permission mapping first
      if self.class.action_permission_mappings&.key?(action_sym)
        permission_or_proc = self.class.action_permission_mappings[action_sym]

        # Handle custom authorization
        authorized = if permission_or_proc.is_a?(Proc)
                       # Execute custom authorization proc in controller context
                       instance_eval(&permission_or_proc)
                     else
                       # Simple permission mapping
                       can?(permission_or_proc, resource_class.name.underscore.to_sym)
                     end

        unless authorized
          render_forbidden("You are not allowed to #{action_name} this #{resource_class.name.underscore}")
          return false
        end

        # If authorized, load the resource
        resource = find_resource(resource_class, {})
        return false if resource == false # find_resource can return false on error

        # Set instance variable
        instance_variable_set("@#{controller_name.singularize}", resource)
        resource
      else
        # Use default authorization logic
        options = {}
        resource = authorize_single_resource(action_sym, resource_class, options)

        # Set instance variable with singular resource name (like @case, @employee)
        instance_variable_set("@#{controller_name.singularize}", resource)
        resource
      end
    end
  end
end
